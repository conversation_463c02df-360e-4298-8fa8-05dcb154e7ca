#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Sentinel-2影像波段可视化工具
用于可视化final_rtm_enhanced00000_sentinel2.tif文件的各个波段
"""

import numpy as np
import matplotlib.pyplot as plt
import rasterio
import os
from matplotlib.colors import LinearSegmentedColormap
import seaborn as sns

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_sentinel2_tif(file_path):
    """加载Sentinel-2 TIF文件"""
    try:
        with rasterio.open(file_path) as src:
            data = src.read().astype(np.float32)
            meta = src.meta.copy()
            
            print(f"✅ 文件加载成功: {os.path.basename(file_path)}")
            print(f"   数据形状: {data.shape}")
            print(f"   波段数量: {data.shape[0]}")
            print(f"   空间分辨率: {data.shape[1]} x {data.shape[2]}")
            print(f"   数据类型: {data.dtype}")
            print(f"   数值范围: {np.nanmin(data):.6f} - {np.nanmax(data):.6f}")
            
            return data, meta
            
    except Exception as e:
        print(f"❌ 文件加载失败: {e}")
        return None, None

def analyze_band_statistics(data, band_names):
    """分析各波段统计信息"""
    print("\n" + "="*60)
    print("波段统计信息分析")
    print("="*60)
    
    for i, band_name in enumerate(band_names):
        if i < data.shape[0]:
            band_data = data[i]
            print(f"\n{band_name} 波段:")
            print(f"  最小值: {np.nanmin(band_data):.6f}")
            print(f"  最大值: {np.nanmax(band_data):.6f}")
            print(f"  平均值: {np.nanmean(band_data):.6f}")
            print(f"  标准差: {np.nanstd(band_data):.6f}")
            print(f"  有效像素: {np.sum(~np.isnan(band_data))}")
            print(f"  无效像素: {np.sum(np.isnan(band_data))}")

def create_individual_band_plots(data, band_names, save_dir="band_plots"):
    """为每个波段创建单独的详细图"""
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
    
    print(f"\n正在创建各波段单独图像...")
    
    for i, band_name in enumerate(band_names):
        if i < data.shape[0]:
            band_data = data[i]
            
            # 创建单个波段的详细图
            fig, axes = plt.subplots(2, 2, figsize=(15, 12))
            fig.suptitle(f'Sentinel-2 {band_name} 波段详细分析', fontsize=16, fontweight='bold')
            
            # 原始数据显示
            im1 = axes[0, 0].imshow(band_data, cmap='viridis', origin='lower')
            axes[0, 0].set_title(f'{band_name} 原始数据')
            axes[0, 0].axis('off')
            plt.colorbar(im1, ax=axes[0, 0], shrink=0.8)
            
            # 增强对比度显示
            vmin, vmax = np.nanpercentile(band_data, [2, 98])
            im2 = axes[0, 1].imshow(band_data, cmap='viridis', origin='lower', vmin=vmin, vmax=vmax)
            axes[0, 1].set_title(f'{band_name} 增强对比度 (2%-98%)')
            axes[0, 1].axis('off')
            plt.colorbar(im2, ax=axes[0, 1], shrink=0.8)
            
            # 直方图
            valid_data = band_data[~np.isnan(band_data)]
            axes[1, 0].hist(valid_data.flatten(), bins=100, alpha=0.7, color='skyblue', edgecolor='black')
            axes[1, 0].set_title(f'{band_name} 数值分布直方图')
            axes[1, 0].set_xlabel('反射率值')
            axes[1, 0].set_ylabel('像素数量')
            axes[1, 0].grid(True, alpha=0.3)
            
            # 热力图风格
            sns.heatmap(band_data, cmap='RdYlBu_r', cbar=True, ax=axes[1, 1],
                       vmin=np.nanpercentile(band_data, 5), vmax=np.nanpercentile(band_data, 95))
            axes[1, 1].set_title(f'{band_name} 热力图显示')
            
            plt.tight_layout()
            save_path = os.path.join(save_dir, f'{band_name}_detailed_analysis.png')
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            print(f"  ✅ {band_name} 波段图已保存: {save_path}")

def create_all_bands_overview(data, band_names, save_path="all_bands_overview.png"):
    """创建所有波段的总览图"""
    n_bands = min(len(band_names), data.shape[0])
    
    # 计算子图布局
    if n_bands <= 3:
        rows, cols = 1, n_bands
        figsize = (5*n_bands, 5)
    elif n_bands <= 6:
        rows, cols = 2, 3
        figsize = (15, 10)
    else:
        rows, cols = 3, 3
        figsize = (15, 15)
    
    fig, axes = plt.subplots(rows, cols, figsize=figsize)
    if n_bands == 1:
        axes = [axes]
    elif rows == 1 or cols == 1:
        axes = axes.flatten()
    else:
        axes = axes.flatten()
    
    fig.suptitle('Sentinel-2 所有波段总览', fontsize=16, fontweight='bold')
    
    for i in range(n_bands):
        band_data = data[i]
        band_name = band_names[i]
        
        # 使用增强对比度显示
        vmin, vmax = np.nanpercentile(band_data, [2, 98])
        im = axes[i].imshow(band_data, cmap='viridis', origin='lower', vmin=vmin, vmax=vmax)
        axes[i].set_title(f'{band_name}\n范围: {np.nanmin(band_data):.4f} - {np.nanmax(band_data):.4f}')
        axes[i].axis('off')
        plt.colorbar(im, ax=axes[i], shrink=0.8)
    
    # 隐藏多余的子图
    for i in range(n_bands, len(axes)):
        axes[i].axis('off')
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"✅ 所有波段总览图已保存: {save_path}")
    plt.show()

def create_band_comparison(data, band_names, save_path="band_comparison.png"):
    """创建波段对比分析图"""
    if data.shape[0] < 2:
        print("波段数量不足，无法进行对比分析")
        return
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('Sentinel-2 波段对比分析', fontsize=16, fontweight='bold')
    
    # 显示前几个主要波段
    main_bands = min(4, data.shape[0])
    for i in range(main_bands):
        row = i // 2
        col = i % 2
        if row < 2 and col < 2:
            band_data = data[i]
            vmin, vmax = np.nanpercentile(band_data, [2, 98])
            im = axes[row, col].imshow(band_data, cmap='viridis', origin='lower', vmin=vmin, vmax=vmax)
            axes[row, col].set_title(f'{band_names[i]} 波段')
            axes[row, col].axis('off')
            plt.colorbar(im, ax=axes[row, col], shrink=0.8)
    
    # 如果有B11和B12波段，计算比值
    if data.shape[0] >= 5:  # 假设B11是索引3，B12是索引4
        b11_data = data[3]
        b12_data = data[4]
        ratio = b11_data / (b12_data + 1e-8)
        
        im = axes[0, 2].imshow(ratio, cmap='RdYlBu_r', origin='lower')
        axes[0, 2].set_title('B11/B12 比值')
        axes[0, 2].axis('off')
        plt.colorbar(im, ax=axes[0, 2], shrink=0.8)
        
        # 差值分析
        diff = b11_data - b12_data
        im = axes[1, 2].imshow(diff, cmap='RdBu_r', origin='lower')
        axes[1, 2].set_title('B11 - B12 差值')
        axes[1, 2].axis('off')
        plt.colorbar(im, ax=axes[1, 2], shrink=0.8)
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"✅ 波段对比分析图已保存: {save_path}")
    plt.show()

def main():
    """主函数"""
    print("="*70)
    print("Sentinel-2 影像波段可视化工具")
    print("="*70)
    
    # 文件路径
    tif_file = "final_rtm_enhanced00000_sentinel2.tif"
    
    # 检查文件是否存在
    if not os.path.exists(tif_file):
        print(f"❌ 文件不存在: {tif_file}")
        print("请确保文件在当前目录下")
        return
    
    # 定义波段名称（根据Sentinel-2标准）
    band_names = ['B3 (Green)', 'B4 (Red)', 'B8 (NIR)', 'B11 (SWIR1)', 'B12 (SWIR2)']
    
    print(f"\n正在加载文件: {tif_file}")
    data, meta = load_sentinel2_tif(tif_file)
    
    if data is None:
        return
    
    # 分析波段统计信息
    analyze_band_statistics(data, band_names)
    
    # 创建各种可视化
    print(f"\n开始创建可视化图像...")
    
    # 1. 所有波段总览
    create_all_bands_overview(data, band_names)
    
    # 2. 波段对比分析
    create_band_comparison(data, band_names)
    
    # 3. 各波段详细分析
    create_individual_band_plots(data, band_names)
    
    print(f"\n🎉 所有可视化图像创建完成！")
    print(f"   - 总览图: all_bands_overview.png")
    print(f"   - 对比图: band_comparison.png") 
    print(f"   - 详细图: band_plots/ 文件夹中")

if __name__ == "__main__":
    main()
