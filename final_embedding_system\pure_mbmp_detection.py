#!/usr/bin/env python3
"""
纯净版MBMP甲烷检测算法
不依赖任何原始羽流数据，纯粹基于影像进行盲检测

作者: AI Assistant
日期: 2025-07-27
版本: Pure v1.0
"""

import pickle
from math import radians, cos
import rasterio
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os

# 设置中文字体和图形参数
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 100

def extract_sun_angles_from_excel(excel_file, tif_filename):
    """从Excel文件中提取指定TIF文件的太阳角度信息"""
    df = pd.read_excel(excel_file)
    
    # 查找匹配的文件名
    base_filename = os.path.basename(tif_filename)
    matching_row = None
    
    # 提取日期信息进行匹配
    import re
    date_match = re.search(r'(\d{4}-\d{2}-\d{2})', base_filename)
    if date_match:
        target_date = date_match.group(1)
        print(f"正在查找日期 {target_date} 的角度信息...")
        
        for idx, row in df.iterrows():
            excel_filename = str(row['TIF文件名'])
            if target_date in excel_filename:
                matching_row = row
                print(f"找到匹配记录: {excel_filename}")
                break
    
    if matching_row is None:
        print("未找到精确匹配，使用第一条记录的角度信息")
        matching_row = df.iloc[0]
    
    # 提取角度信息
    zenith_angle = float(matching_row['太阳天顶角'])
    vza = float(matching_row['B11波段天顶角'])
    
    print(f"使用文件: {matching_row['TIF文件名']}")
    print(f"太阳天顶角: {zenith_angle:.2f}°")
    print(f"观测天顶角: {vza:.2f}°")
    
    return zenith_angle, vza

def extract_bands_from_5band_tif(tif_file, b11_index=3, b12_index=4):
    """从5波段TIF文件中提取B11和B12波段"""
    try:
        with rasterio.open(tif_file) as src:
            all_bands = src.read()
            b11_data = all_bands[b11_index]
            b12_data = all_bands[b12_index]
            
            # 修复方向问题：确保与显示坐标系一致
            if "S2_Combined" in os.path.basename(tif_file):
                b11_data = np.flipud(b11_data)
                b12_data = np.flipud(b12_data)
                print("✅ 已修正原始TIFF数据方向")
            
            print(f"从文件 {os.path.basename(tif_file)} 提取波段:")
            print(f"B11波段: 形状{b11_data.shape}, 范围{np.nanmin(b11_data):.3f}-{np.nanmax(b11_data):.3f}")
            print(f"B12波段: 形状{b12_data.shape}, 范围{np.nanmin(b12_data):.3f}-{np.nanmax(b12_data):.3f}")
            
            return b11_data, b12_data
            
    except Exception as e:
        print(f"提取波段时出错: {e}")
        return None, None

def f_young(za):
    """Young's air mass factor calculation"""
    za = radians(za)
    f = (1.002432*(cos(za))**2+0.148386*cos(za)+0.0096467) / \
        ((cos(za))**3+0.149864*(cos(za))**2+0.0102963*cos(za)+0.000303978)
    return f

def giveamf(sza, vza):
    """Calculate air mass factor"""
    rair = f_young(sza) + f_young(vza)
    return rair

def fullMBMP2Omega(delr, satellite, sza, vza):
    """Convert MBMP delta R to methane column concentration"""
    # 尝试加载pickle文件
    pickle_paths = [
        r"D:\jiawan3.11\7.26 - 副本 (2)\11\mbmp\S2_full_mdata_poly_10_delr_to_omega.pkl",
        "../11/mbmp/S2_full_mdata_poly_10_delr_to_omega.pkl",
        "S2_full_mdata_poly_10_delr_to_omega.pkl"
    ]
    
    mdata = None
    for pickle_path in pickle_paths:
        try:
            mdata = pickle.load(open(pickle_path, 'rb'))
            print(f"✅ 成功加载MBMP转换数据: {pickle_path}")
            break
        except FileNotFoundError:
            continue
    
    if mdata is None:
        print("❌ 无法找到MBMP转换数据文件")
        return None
    
    tamf = giveamf(sza, vza)
    
    try:
        coefficients = mdata[satellite]['MBMP'][tamf]
    except KeyError:
        closest_key = min(mdata[satellite]['MBMP'].keys(), key=lambda x: abs(x - tamf))
        coefficients = mdata[satellite]['MBMP'][closest_key]
        print(f"使用最接近的AMF值: {closest_key:.3f} (目标: {tamf:.3f})")
    
    omega = np.polyval(coefficients, delr)
    return omega

def mbmpk(R12, R11, R12old, R11old):
    """计算MBMP算法的差分比值"""
    # 数据预处理 - 处理参考影像的数字计数值
    if np.max(R12old) > 10:  # 如果是数字计数值，转换为反射率
        R12old = R12old * 0.0001
        R11old = R11old * 0.0001
        print("参考影像已转换为反射率")
    
    # 归一化处理
    R12_norm = R12 / np.nanmedian(R12)
    R11_norm = R11 / np.nanmedian(R11)
    R12old_norm = R12old / np.nanmedian(R12old)
    R11old_norm = R11old / np.nanmedian(R11old)
    
    # 计算比值
    delR1 = np.divide(R12_norm, R11_norm, out=np.zeros_like(R12_norm), where=R11_norm!=0)
    delR2 = np.divide(R12old_norm, R11old_norm, out=np.zeros_like(R12old_norm), where=R11old_norm!=0)
    
    # 计算差分
    delR = delR1 - delR2
    
    print(f"差分比值统计:")
    print(f"  范围: {np.nanmin(delR):.6f} 到 {np.nanmax(delR):.6f}")
    print(f"  平均值: {np.nanmean(delR):.6f}")
    print(f"  标准差: {np.nanstd(delR):.6f}")
    
    return delR

def create_pure_mbmp_visualization(delR, omega, R11, R12, R11old, R12old,
                                  save_path="pure_mbmp_detection.png"):
    """创建纯净版MBMP检测可视化（不包含原始羽流数据）"""
    fig, axes = plt.subplots(2, 4, figsize=(20, 10))

    # 第一行：输入数据
    im1 = axes[0, 0].imshow(R11, cmap='viridis', origin='lower')
    axes[0, 0].set_title('目标影像 B11波段')
    axes[0, 0].axis('off')
    plt.colorbar(im1, ax=axes[0, 0], shrink=0.8)

    im2 = axes[0, 1].imshow(R12, cmap='viridis', origin='lower')
    axes[0, 1].set_title('目标影像 B12波段')
    axes[0, 1].axis('off')
    plt.colorbar(im2, ax=axes[0, 1], shrink=0.8)

    # 参考影像数据
    R11old_display = R11old * 0.0001 if np.max(R11old) > 10 else R11old
    R12old_display = R12old * 0.0001 if np.max(R12old) > 10 else R12old

    im3 = axes[0, 2].imshow(R11old_display, cmap='viridis', origin='lower')
    axes[0, 2].set_title('参考影像 B11波段')
    axes[0, 2].axis('off')
    plt.colorbar(im3, ax=axes[0, 2], shrink=0.8)

    im4 = axes[0, 3].imshow(R12old_display, cmap='viridis', origin='lower')
    axes[0, 3].set_title('参考影像 B12波段')
    axes[0, 3].axis('off')
    plt.colorbar(im4, ax=axes[0, 3], shrink=0.8)

    # 第二行：MBMP检测结果
    im5 = axes[1, 0].imshow(delR, cmap='RdBu_r', origin='lower',
                           vmin=np.nanpercentile(delR, 5), vmax=np.nanpercentile(delR, 95))
    axes[1, 0].set_title('MBMP差分比值 (ΔR)')
    axes[1, 0].axis('off')
    plt.colorbar(im5, ax=axes[1, 0], shrink=0.8)

    im6 = axes[1, 1].imshow(omega, cmap='RdBu_r', origin='lower',
                           vmin=np.nanpercentile(omega, 5), vmax=np.nanpercentile(omega, 95))
    axes[1, 1].set_title('甲烷柱浓度 (mol/m²)')
    axes[1, 1].axis('off')
    plt.colorbar(im6, ax=axes[1, 1], shrink=0.8)

    # 不同阈值的甲烷检测结果
    threshold1 = 0.05
    methane_detection1 = omega > threshold1
    im7 = axes[1, 2].imshow(methane_detection1, cmap='RdYlGn', origin='lower')
    axes[1, 2].set_title(f'甲烷检测 (阈值>{threshold1} mol/m²)')
    axes[1, 2].axis('off')
    plt.colorbar(im7, ax=axes[1, 2], shrink=0.8)

    threshold2 = 0.1
    methane_detection2 = omega > threshold2
    im8 = axes[1, 3].imshow(methane_detection2, cmap='RdYlGn', origin='lower')
    axes[1, 3].set_title(f'甲烷检测 (阈值>{threshold2} mol/m²)')
    axes[1, 3].axis('off')
    plt.colorbar(im8, ax=axes[1, 3], shrink=0.8)

    plt.suptitle('纯净版MBMP甲烷检测结果', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"纯净版MBMP检测图已保存: {save_path}")
    plt.close()

def create_final_result_visualization(omega, R11, R12, save_path="mbmp_final_result.png"):
    """创建最终结果可视化 - 带背景影像的甲烷检测结果"""
    import seaborn as sns
    from matplotlib.colors import LinearSegmentedColormap

    # 计算统计信息
    max_concentration = np.nanmax(omega)

    # 创建复合可视化
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))

    # 第一个子图：背景影像（B11波段）
    im1 = axes[0].imshow(R11, cmap='gray', origin='lower')
    axes[0].set_title('Background Image (B11 Band)', fontsize=12, fontweight='bold')
    axes[0].set_xlabel('Pixel X')
    axes[0].set_ylabel('Pixel Y')
    plt.colorbar(im1, ax=axes[0], shrink=0.8)

    # 第二个子图：甲烷检测结果热图（参考您的代码风格）
    # 使用seaborn热图，类似您提供的代码
    sns.heatmap(omega, annot=False, cmap="RdBu_r", cbar=True,
                vmin=np.nanmin(omega), vmax=np.nanmax(omega), ax=axes[1])
    axes[1].set_title(f'MBMP Methane Detection\nMax: {max_concentration:.6f} mol/m²',
                     fontsize=12, fontweight='bold')
    axes[1].set_xlabel('Pixel X')
    axes[1].set_ylabel('Pixel Y')

    # 第三个子图：背景+甲烷叠加显示
    # 创建背景影像的归一化版本
    background_norm = (R11 - np.nanmin(R11)) / (np.nanmax(R11) - np.nanmin(R11))

    # 创建甲烷掩膜（只显示高浓度区域）
    methane_threshold = np.nanpercentile(omega, 95)  # 使用95%分位数作为阈值
    methane_mask = omega > methane_threshold

    # 显示背景
    axes[2].imshow(background_norm, cmap='gray', origin='lower', alpha=0.8)

    # 叠加甲烷检测结果（只显示高浓度区域）
    masked_omega = np.ma.masked_where(~methane_mask, omega)
    im3 = axes[2].imshow(masked_omega, cmap='hot', origin='lower', alpha=0.7)

    axes[2].set_title('Background + Methane Overlay', fontsize=12, fontweight='bold')
    axes[2].set_xlabel('Pixel X')
    axes[2].set_ylabel('Pixel Y')
    plt.colorbar(im3, ax=axes[2], shrink=0.8, label='Methane (mol/m²)')

    # 调整布局
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"最终结果图已保存: {save_path}")
    plt.close()

    return max_concentration

def create_seaborn_style_result(omega, save_path="mbmp_seaborn_style.png"):
    """创建seaborn风格的甲烷检测结果图 - 类似您展示的效果"""
    import seaborn as sns

    # 计算统计信息
    max_concentration = np.nanmax(omega)
    min_concentration = np.nanmin(omega)

    # 创建单个大图
    plt.figure(figsize=(12, 10))

    # 使用seaborn热图，参考您提供的代码风格
    # 关键：使用完整的数据范围，让背景纹理可见
    sns.heatmap(omega,
                annot=False,
                cmap="RdBu_r",
                cbar=True,
                vmin=min_concentration,  # 使用最小值
                vmax=max_concentration,  # 使用最大值
                square=False,
                cbar_kws={'label': 'Methane Column Concentration (mol/m²)'})

    # 添加标题和信息
    title = f'MBMP Methane Detection (mol/m²)\n'
    title += f'Target Location: Lat 31.6585, Lon 5.9053\n'
    title += f'Max Concentration: {max_concentration:.6f} mol/m²'
    plt.title(title, fontsize=14, fontweight='bold', pad=20)

    # 设置坐标轴标签
    plt.xlabel('Pixel X', fontsize=12)
    plt.ylabel('Pixel Y', fontsize=12)

    # 调整布局
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"Seaborn风格结果图已保存: {save_path}")
    plt.close()

    return max_concentration

def create_enhanced_seaborn_result(omega, save_path="mbmp_enhanced_seaborn.png"):
    """创建增强版seaborn风格结果图 - 优化背景和羽流显示"""
    import seaborn as sns

    # 计算统计信息
    max_concentration = np.nanmax(omega)
    min_concentration = np.nanmin(omega)

    # 创建单个大图
    plt.figure(figsize=(12, 10))

    # 计算合适的颜色范围，突出羽流同时保持背景可见
    # 使用百分位数来设置颜色范围
    vmin = np.nanpercentile(omega, 1)   # 使用1%分位数作为最小值
    vmax = np.nanpercentile(omega, 99)  # 使用99%分位数作为最大值

    # 如果检测到明显的甲烷信号，调整颜色范围
    if max_concentration > 0.01:  # 如果有显著的甲烷信号
        # 保持负值范围以显示背景纹理
        vmin = min(vmin, -0.1)
        # 适当限制正值范围以突出羽流
        vmax = min(vmax, max_concentration * 1.2)

    # 使用seaborn热图
    ax = sns.heatmap(omega,
                     annot=False,
                     cmap="RdBu_r",
                     cbar=True,
                     vmin=vmin,
                     vmax=vmax,
                     square=False,
                     cbar_kws={'label': 'Methane Column Concentration (mol/m²)'})

    # 添加标题和信息
    title = f'MBMP Methane Detection (mol/m²)\n'
    title += f'Target Location: Lat 31.6585, Lon 5.9053\n'
    title += f'Max Concentration: {max_concentration:.6f} mol/m²'
    plt.title(title, fontsize=14, fontweight='bold', pad=20)

    # 设置坐标轴标签
    plt.xlabel('Pixel X', fontsize=12)
    plt.ylabel('Pixel Y', fontsize=12)

    # 调整布局
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"增强版Seaborn风格结果图已保存: {save_path}")
    plt.close()

    return max_concentration

def analyze_detection_results(omega, thresholds=[0.01, 0.05, 0.1, 0.2, 0.5]):
    """分析检测结果统计"""
    print(f"\n📊 甲烷检测结果分析:")
    print("=" * 50)
    
    total_pixels = np.sum(~np.isnan(omega))
    
    print(f"甲烷柱浓度统计:")
    print(f"  最大值: {np.nanmax(omega):.6f} mol/m²")
    print(f"  最小值: {np.nanmin(omega):.6f} mol/m²")
    print(f"  平均值: {np.nanmean(omega):.6f} mol/m²")
    print(f"  标准差: {np.nanstd(omega):.6f} mol/m²")
    print(f"  有效像元数: {total_pixels}")
    
    print(f"\n不同阈值下的甲烷检测统计:")
    print("阈值(mol/m²)\t检测像元数\t检测率(%)\t最大浓度")
    print("-" * 60)
    
    for threshold in thresholds:
        positive_pixels = np.sum(omega > threshold)
        detection_rate = (positive_pixels / total_pixels) * 100
        max_in_detected = np.nanmax(omega[omega > threshold]) if positive_pixels > 0 else 0
        print(f"{threshold:8.2f}\t\t{positive_pixels:8d}\t{detection_rate:7.2f}\t{max_in_detected:8.6f}")

def main():
    """主函数 - 纯净版MBMP检测"""
    print("="*70)
    print("纯净版MBMP甲烷检测算法")
    print("基于影像数据的盲检测，不依赖任何原始羽流信息")
    print("="*70)
    
    # 文件路径
    RTM_ENHANCED_TIF = r"D:\jiawan3.11\7.26 - 副本 (2)\11\纬度38.4939_经度54.1976\S2_Combined_2021-06-18_15-19-01_NAORBIT_39SYC_N0300.tif"  # RTM增强影像
    REF_TIF = r"D:\jiawan3.11\7.26 - 副本 (2)\11\纬度38.4939_经度54.1976\S2_Combined_2021-06-23_15-19-01_NAORBIT_40SBH_N0300.tif"  # 参考影像
    EXCEL_FILE = r"D:\jiawan3.11\7.26 - 副本 (2)\11\纬度38.4939_经度54.1976\所有日期角度信息汇总.xlsx"  # 角度信息
    
    # 波段索引
    B11_INDEX = 3
    B12_INDEX = 4
    
    print("\n1. 加载目标影像（可能含甲烷）...")
    R11, R12 = extract_bands_from_5band_tif(RTM_ENHANCED_TIF, B11_INDEX, B12_INDEX)
    
    print("\n2. 加载参考影像（无甲烷）...")
    R11old, R12old = extract_bands_from_5band_tif(REF_TIF, B11_INDEX, B12_INDEX)
    
    if R11 is None or R12 is None or R11old is None or R12old is None:
        print("❌ 数据加载失败，请检查文件路径")
        return
    
    print("\n3. 计算MBMP差分比值...")
    delR = mbmpk(R12, R11, R12old, R11old)
    
    print("\n4. 提取观测角度信息...")
    try:
        sza, vza = extract_sun_angles_from_excel(EXCEL_FILE, REF_TIF)
    except Exception as e:
        print(f"提取角度信息失败: {e}")
        print("使用默认角度值...")
        sza, vza = 20.72, 4.81
    
    print(f"\n5. 转换为甲烷柱浓度...")
    print(f"使用角度 - 太阳天顶角: {sza:.2f}°, 观测天顶角: {vza:.2f}°")
    
    satellite = 'S2'
    omega = fullMBMP2Omega(delR, satellite, sza, vza)
    
    if omega is None:
        print("❌ MBMP转换失败，程序退出")
        return
    
    # 分析检测结果
    analyze_detection_results(omega)
    
    print("\n6. 生成增强版Seaborn风格结果图...")
    max_conc = create_enhanced_seaborn_result(omega, "mbmp_enhanced_seaborn.png")
    
    # 保存检测结果
    print("\n7. 保存检测结果数据...")
    output_meta = {
        'driver': 'GTiff',
        'dtype': 'float32',
        'nodata': np.nan,
        'width': omega.shape[1],
        'height': omega.shape[0],
        'count': 1,
        'crs': 'EPSG:3857',
        'compress': 'lzw'
    }
    
    with rasterio.open("pure_mbmp_methane_column.tif", 'w', **output_meta) as dst:
        dst.write(omega.astype(np.float32), 1)
    print("甲烷柱浓度检测结果已保存: pure_mbmp_methane_column.tif")
    
    with rasterio.open("pure_mbmp_delta_r.tif", 'w', **output_meta) as dst:
        dst.write(delR.astype(np.float32), 1)
    print("MBMP差分比值已保存: pure_mbmp_delta_r.tif")
    
    # 输出最终检测结论
    print("\n" + "="*70)
    print("🎯 MBMP甲烷检测结论:")
    
    # 使用多个阈值判断
    thresholds = [0.01, 0.05, 0.1]
    for threshold in thresholds:
        detected_pixels = np.sum(omega > threshold)
        if detected_pixels > 0:
            max_concentration = np.nanmax(omega[omega > threshold])
            print(f"✅ 在阈值 {threshold} mol/m² 下检测到 {detected_pixels} 个甲烷像元")
            print(f"   最大甲烷柱浓度: {max_concentration:.6f} mol/m²")
        else:
            print(f"❌ 在阈值 {threshold} mol/m² 下未检测到甲烷")
    
    print("\n🎉 纯净版MBMP检测完成！")
    print("这是基于影像数据的真实甲烷检测结果，未使用任何先验羽流信息。")
    print("="*70)

if __name__ == "__main__":
    main()
