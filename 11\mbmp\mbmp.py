import pickle
from math import radians, cos
import rasterio
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def extract_sun_angles_from_excel(excel_file, tif_filename):
    """
    从Excel文件中提取指定TIF文件的太阳角度信息
    """
    df = pd.read_excel(excel_file)
    
    # 查找匹配的文件名 - 使用更灵活的匹配方式
    base_filename = os.path.basename(tif_filename)
    matching_row = None
    
    # 提取关键的日期时间信息进行匹配
    # 例如从 "S2_Combined_2021-06-23_15-19-01_NAORBIT_40SBH_N0300.tif" 提取 "2021-06-23"
    import re
    date_match = re.search(r'(\d{4}-\d{2}-\d{2})', base_filename)
    if date_match:
        target_date = date_match.group(1)
        print(f"正在查找日期 {target_date} 的角度信息...")
        
        # 在Excel中查找包含相同日期的记录
        for idx, row in df.iterrows():
            excel_filename = str(row['TIF文件名'])
            if target_date in excel_filename:
                matching_row = row
                print(f"找到匹配记录: {excel_filename}")
                break
    
    # 如果按日期匹配失败，尝试其他匹配方式
    if matching_row is None:
        for idx, row in df.iterrows():
            excel_filename = str(row['TIF文件名'])
            # 尝试部分匹配
            if any(part in excel_filename for part in base_filename.split('_')[:3]):
                matching_row = row
                print(f"找到部分匹配记录: {excel_filename}")
                break
    
    if matching_row is None:
        # 如果没有找到匹配，显示可用的文件名并使用第一条记录
        print("可用的TIF文件名:")
        for filename in df['TIF文件名']:
            print(f"  {filename}")
        print(f"未找到精确匹配，使用第一条记录的角度信息")
        matching_row = df.iloc[0]
    
    # 提取角度信息
    zenith_angle = float(matching_row['太阳天顶角'])
    # 如果有B11波段天顶角，使用它作为观测天顶角，否则使用太阳天顶角
    if 'B11波段天顶角' in df.columns:
        vza = float(matching_row['B11波段天顶角'])
    else:
        vza = zenith_angle  # 如果没有观测角度，使用太阳天顶角作为近似
    
    print(f"使用文件: {matching_row['TIF文件名']}")
    print(f"太阳天顶角: {zenith_angle}°")
    print(f"观测天顶角: {vza}°")
    
    return zenith_angle, vza
def plot_threshold_heatmap(data, threshold=1.0, title="Threshold Heatmap"):
    cmap = plt.cm.gray
    cmap.set_over('white')
    cmap.set_under('black')
    plt.figure(figsize=(8, 6))
    plt.imshow(data, cmap=cmap, vmin=threshold, vmax=threshold + 0.01)  # 设置颜色范围
    plt.colorbar(label='Value')
    plt.title(title)
    plt.show()
def read_tif_bands(input_file, band_indices=None):
    '''
    读取TIF文件的指定波段数据
    band_indices: 要读取的波段索引列表 (0-based)，如果为None则读取所有波段
    对于5波段数据 (B3, B4, B8, B11, B12)，B11对应索引3，B12对应索引4
    '''
    try:
        with rasterio.open(input_file) as src:
            if band_indices is None:
                # 读取所有波段
                data = src.read()
            else:
                # 读取指定波段 (rasterio使用1-based索引)
                data = src.read([i+1 for i in band_indices])
            
            print(f"读取文件: {os.path.basename(input_file)}")
            print(f"数据形状: {data.shape}")
            print(f"数据类型: {data.dtype}")
            print(f"数据范围: {np.nanmin(data)} 到 {np.nanmax(data)}")
            
            return data
    except Exception as e:
        print(f"处理文件时出错: {e}")
        return None

def extract_bands_from_5band_tif(tif_file, b11_index=3, b12_index=4):
    """
    从5波段TIF文件中提取B11和B12波段
    tif_file: 5波段TIF文件路径
    b11_index: B11波段在文件中的索引 (0-based)
    b12_index: B12波段在文件中的索引 (0-based)
    返回: (B11_data, B12_data)
    """
    try:
        with rasterio.open(tif_file) as src:
            # 读取所有波段
            all_bands = src.read()
            
            # 提取B11和B12波段
            b11_data = all_bands[b11_index]
            b12_data = all_bands[b12_index]
            
            print(f"从文件 {os.path.basename(tif_file)} 提取波段:")
            print(f"B11波段 (索引{b11_index}): 形状{b11_data.shape}, 范围{np.nanmin(b11_data):.2f}-{np.nanmax(b11_data):.2f}")
            print(f"B12波段 (索引{b12_index}): 形状{b12_data.shape}, 范围{np.nanmin(b12_data):.2f}-{np.nanmax(b12_data):.2f}")
            
            return b11_data, b12_data
            
    except Exception as e:
        print(f"提取波段时出错: {e}")
        return None, None

def f_young(za):
         za=radians(za)
         f=(1.002432*(cos(za))**2+0.148386*cos(za)+0.0096467)\
         /((cos(za))**3+0.149864*(cos(za))**2+0.0102963*cos(za)+0.000303978)
         return f
def giveamf(sza, vza):
    rair=f_young(sza)+f_young(vza)
    return rair

def fullMBMP2Omega(delr, satellite, sza, vza):
    # 使用脚本所在目录的路径
    script_dir = os.path.dirname(os.path.abspath(__file__))
    pickle_path = os.path.join(script_dir, 'S2_full_mdata_poly_10_delr_to_omega.pkl')
    mdata = pickle.load(open(pickle_path, 'rb'))
    tamf = giveamf(sza, vza)
    try:
        coefficients = mdata[satellite]['MBMP'][tamf]
    except KeyError as e:
        closest_key = min(mdata[satellite]['MBMP'].keys(), key=lambda x: abs(x - tamf))
        coefficients = mdata[satellite]['MBMP'][closest_key]
    omega = np.polyval(coefficients, delr)
    return omega
def mbmpk(R12, R11, R12old, R11old):
    """
    计算MBMP算法的差分比值
    R12, R11: 甲烷影像的B12和B11波段
    R12old, R11old: 参考影像的B12和B11波段
    """
    # 归一化处理
    R12_norm = R12 / np.nanmedian(R12)
    R11_norm = R11 / np.nanmedian(R11)
    R12old_norm = R12old / np.nanmedian(R12old)
    R11old_norm = R11old / np.nanmedian(R11old)
    
    # 计算比值
    delR1 = np.divide(R12_norm, R11_norm, out=np.zeros_like(R12_norm), where=R11_norm!=0)
    delR2 = np.divide(R12old_norm, R11old_norm, out=np.zeros_like(R12old_norm), where=R11old_norm!=0)
    
    # 计算差分
    delR = delR1 - delR2
    
    print(f"差分比值范围: {np.nanmin(delR)} 到 {np.nanmax(delR)}")
    
    return delR

def main():
    # 更新的数据路径
    PLUME_TIF = r"D:\jiawan3.11\7.26 - 副本 (2)\final_rtm_enhanced00000_sentinel2.tif"  # 含有甲烷羽流的合并影像
    REF_TIF = r"D:\jiawan3.11\7.26 - 副本 (2)\11\纬度38.4939_经度54.1976\S2_Combined_2021-06-23_15-19-01_NAORBIT_40SBH_N0300.tif"  # 参考影像
    EXCEL_FILE = r"D:\jiawan3.11\7.26 - 副本 (2)\11\纬度38.4939_经度54.1976\所有日期角度信息汇总.xlsx"  # 角度信息Excel文件
    
    # 5波段TIF文件中的波段索引 (0-based): B3, B4, B8, B11, B12
    # B11对应索引3，B12对应索引4
    B11_INDEX = 3
    B12_INDEX = 4
    
    print("正在处理含羽流影像...")
    # 从含羽流影像中提取B11和B12波段
    R11, R12 = extract_bands_from_5band_tif(PLUME_TIF, B11_INDEX, B12_INDEX)
    
    print("\n正在处理参考影像...")
    # 从参考影像中提取B11和B12波段
    R11old, R12old = extract_bands_from_5band_tif(REF_TIF, B11_INDEX, B12_INDEX)
    
    if R11 is None or R12 is None or R11old is None or R12old is None:
        print("波段提取失败，请检查文件路径和波段索引")
        return
    
    print("\n正在计算MBMP差分比值...")
    delR = mbmpk(R12, R11, R12old, R11old)
    
    print("\n正在从Excel文件提取角度信息...")
    # 从Excel文件中提取角度信息，使用参考影像的文件名
    try:
        sza, vza = extract_sun_angles_from_excel(EXCEL_FILE, REF_TIF)
    except Exception as e:
        print(f"提取角度信息失败: {e}")
        print("使用默认角度值...")
        sza, vza = 30.0, 30.0  # 默认角度值
    
    print(f"\n使用角度信息 - 太阳天顶角: {sza}°, 观测天顶角: {vza}°")
    
    satellite = 'S2'  # Sentinel-2
    print("\n正在计算甲烷柱浓度...")
    omega = fullMBMP2Omega(delR, satellite, sza, vza)
    
    print(f"\n甲烷柱浓度 (mol/m-2):")
    print(f"最大值: {np.nanmax(omega):.6f}")
    print(f"最小值: {np.nanmin(omega):.6f}")
    print(f"平均值: {np.nanmean(omega):.6f}")
    
    # 可视化结果
    plt.figure(figsize=(12, 5))
    
    # 子图1: 差分比值
    plt.subplot(1, 2, 1)
    plt.imshow(delR, cmap='RdBu_r', vmin=np.nanpercentile(delR, 5), vmax=np.nanpercentile(delR, 95))
    plt.colorbar(label='Delta R')
    plt.title('MBMP差分比值')
    
    # 子图2: 甲烷柱浓度
    plt.subplot(1, 2, 2)
    plt.imshow(omega, cmap='RdBu_r', vmin=np.nanpercentile(omega, 5), vmax=np.nanpercentile(omega, 95))
    plt.colorbar(label='CH4 Column (mol/m²)')
    plt.title('甲烷柱浓度')
    
    plt.tight_layout()
    plt.show()
    
    # 可选：阈值热图
    # plot_threshold_heatmap(omega, threshold=0.5, title="甲烷浓度阈值图")

if __name__ == "__main__":
    main()
