#!/usr/bin/env python3
"""
最终版本：基于RTM增强数据的MBMP甲烷检测
使用真实甲烷羽流RTM增强数据进行MBMP检测

作者: AI Assistant
日期: 2025-07-27
版本: Final v1.0
"""

import pickle
from math import radians, cos
import rasterio
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os

# 设置中文字体和图形参数
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 100

def extract_sun_angles_from_excel(excel_file, tif_filename):
    """从Excel文件中提取指定TIF文件的太阳角度信息"""
    df = pd.read_excel(excel_file)
    
    # 查找匹配的文件名
    base_filename = os.path.basename(tif_filename)
    matching_row = None
    
    # 提取日期信息进行匹配
    import re
    date_match = re.search(r'(\d{4}-\d{2}-\d{2})', base_filename)
    if date_match:
        target_date = date_match.group(1)
        print(f"正在查找日期 {target_date} 的角度信息...")
        
        for idx, row in df.iterrows():
            excel_filename = str(row['TIF文件名'])
            if target_date in excel_filename:
                matching_row = row
                print(f"找到匹配记录: {excel_filename}")
                break
    
    if matching_row is None:
        print("未找到精确匹配，使用第一条记录的角度信息")
        matching_row = df.iloc[0]
    
    # 提取角度信息
    zenith_angle = float(matching_row['太阳天顶角'])
    vza = float(matching_row['B11波段天顶角'])
    
    print(f"使用文件: {matching_row['TIF文件名']}")
    print(f"太阳天顶角: {zenith_angle:.2f}°")
    print(f"观测天顶角: {vza:.2f}°")
    
    return zenith_angle, vza

def extract_bands_from_5band_tif(tif_file, b11_index=3, b12_index=4):
    """从5波段TIF文件中提取B11和B12波段（修正方向问题）"""
    try:
        with rasterio.open(tif_file) as src:
            all_bands = src.read()
            b11_data = all_bands[b11_index]
            b12_data = all_bands[b12_index]
            
            # 修复方向问题：确保与显示坐标系一致
            # 检查文件名，如果是从rasterio直接读取的原始TIFF，需要翻转
            if "S2_Combined" in os.path.basename(tif_file):
                b11_data = np.flipud(b11_data)
                b12_data = np.flipud(b12_data)
                print("✅ 已修正原始TIFF数据方向")
            
            print(f"从文件 {os.path.basename(tif_file)} 提取波段:")
            print(f"B11波段: 形状{b11_data.shape}, 范围{np.nanmin(b11_data):.3f}-{np.nanmax(b11_data):.3f}")
            print(f"B12波段: 形状{b12_data.shape}, 范围{np.nanmin(b12_data):.3f}-{np.nanmax(b12_data):.3f}")
            
            return b11_data, b12_data
            
    except Exception as e:
        print(f"提取波段时出错: {e}")
        return None, None

def load_original_plume(npy_file):
    """加载原始甲烷羽流数据用于对比"""
    try:
        plume_data = np.load(npy_file)
        print(f"✅ 原始甲烷羽流数据加载成功:")
        print(f"   浓度范围: {np.nanmin(plume_data):.3f} - {np.nanmax(plume_data):.3f} ppm")
        return plume_data
    except Exception as e:
        print(f"❌ 原始羽流数据加载失败: {e}")
        return None

def f_young(za):
    """Young's air mass factor calculation"""
    za = radians(za)
    f = (1.002432*(cos(za))**2+0.148386*cos(za)+0.0096467) / \
        ((cos(za))**3+0.149864*(cos(za))**2+0.0102963*cos(za)+0.000303978)
    return f

def giveamf(sza, vza):
    """Calculate air mass factor"""
    rair = f_young(sza) + f_young(vza)
    return rair

def fullMBMP2Omega(delr, satellite, sza, vza):
    """Convert MBMP delta R to methane column concentration"""
    # 尝试加载pickle文件
    pickle_paths = [
        r"D:\jiawan3.11\7.26 - 副本 (2)\11\mbmp\S2_full_mdata_poly_10_delr_to_omega.pkl",
        "../11/mbmp/S2_full_mdata_poly_10_delr_to_omega.pkl",
        "S2_full_mdata_poly_10_delr_to_omega.pkl"
    ]
    
    mdata = None
    for pickle_path in pickle_paths:
        try:
            mdata = pickle.load(open(pickle_path, 'rb'))
            print(f"✅ 成功加载MBMP转换数据: {pickle_path}")
            break
        except FileNotFoundError:
            continue
    
    if mdata is None:
        print("❌ 无法找到MBMP转换数据文件")
        return None
    
    tamf = giveamf(sza, vza)
    
    try:
        coefficients = mdata[satellite]['MBMP'][tamf]
    except KeyError:
        closest_key = min(mdata[satellite]['MBMP'].keys(), key=lambda x: abs(x - tamf))
        coefficients = mdata[satellite]['MBMP'][closest_key]
        print(f"使用最接近的AMF值: {closest_key:.3f} (目标: {tamf:.3f})")
    
    omega = np.polyval(coefficients, delr)
    return omega

def mbmpk(R12, R11, R12old, R11old):
    """计算MBMP算法的差分比值"""
    # 数据预处理 - 处理参考影像的数字计数值
    if np.max(R12old) > 10:  # 如果是数字计数值，转换为反射率
        R12old = R12old * 0.0001
        R11old = R11old * 0.0001
        print("参考影像已转换为反射率")
    
    # 归一化处理
    R12_norm = R12 / np.nanmedian(R12)
    R11_norm = R11 / np.nanmedian(R11)
    R12old_norm = R12old / np.nanmedian(R12old)
    R11old_norm = R11old / np.nanmedian(R11old)
    
    # 计算比值
    delR1 = np.divide(R12_norm, R11_norm, out=np.zeros_like(R12_norm), where=R11_norm!=0)
    delR2 = np.divide(R12old_norm, R11old_norm, out=np.zeros_like(R12old_norm), where=R11old_norm!=0)
    
    # 计算差分
    delR = delR1 - delR2
    
    print(f"差分比值统计:")
    print(f"  范围: {np.nanmin(delR):.6f} 到 {np.nanmax(delR):.6f}")
    print(f"  平均值: {np.nanmean(delR):.6f}")
    print(f"  标准差: {np.nanstd(delR):.6f}")
    
    return delR

def create_final_mbmp_visualization(delR, omega, R11, R12, R11old, R12old, 
                                   original_plume, save_path="final_mbmp_analysis.png"):
    """创建最终版MBMP分析可视化"""
    fig, axes = plt.subplots(3, 4, figsize=(20, 15))
    
    # 第一行：真实羽流和RTM增强波段
    im1 = axes[0, 0].imshow(original_plume, cmap='hot', origin='lower')
    axes[0, 0].set_title('原始真实甲烷羽流 (ppm)')
    axes[0, 0].axis('off')
    plt.colorbar(im1, ax=axes[0, 0], shrink=0.8)
    
    im2 = axes[0, 1].imshow(R11, cmap='viridis', origin='lower')
    axes[0, 1].set_title('RTM增强 B11波段')
    axes[0, 1].axis('off')
    plt.colorbar(im2, ax=axes[0, 1], shrink=0.8)
    
    im3 = axes[0, 2].imshow(R12, cmap='viridis', origin='lower')
    axes[0, 2].set_title('RTM增强 B12波段')
    axes[0, 2].axis('off')
    plt.colorbar(im3, ax=axes[0, 2], shrink=0.8)
    
    # RTM增强后的B11/B12比值
    ratio_rtm = R11 / (R12 + 1e-8)
    im4 = axes[0, 3].imshow(ratio_rtm, cmap='RdYlBu_r', origin='lower')
    axes[0, 3].set_title('RTM增强 B11/B12比值')
    axes[0, 3].axis('off')
    plt.colorbar(im4, ax=axes[0, 3], shrink=0.8)
    
    # 第二行：参考影像数据
    R11old_display = R11old * 0.0001 if np.max(R11old) > 10 else R11old
    R12old_display = R12old * 0.0001 if np.max(R12old) > 10 else R12old
    
    im5 = axes[1, 0].imshow(R11old_display, cmap='viridis', origin='lower')
    axes[1, 0].set_title('参考影像 B11波段')
    axes[1, 0].axis('off')
    plt.colorbar(im5, ax=axes[1, 0], shrink=0.8)
    
    im6 = axes[1, 1].imshow(R12old_display, cmap='viridis', origin='lower')
    axes[1, 1].set_title('参考影像 B12波段')
    axes[1, 1].axis('off')
    plt.colorbar(im6, ax=axes[1, 1], shrink=0.8)
    
    # 参考影像B11/B12比值
    ratio_ref = R11old_display / (R12old_display + 1e-8)
    im7 = axes[1, 2].imshow(ratio_ref, cmap='RdYlBu_r', origin='lower')
    axes[1, 2].set_title('参考影像 B11/B12比值')
    axes[1, 2].axis('off')
    plt.colorbar(im7, ax=axes[1, 2], shrink=0.8)
    
    # 比值差异
    ratio_diff = ratio_rtm - ratio_ref
    im8 = axes[1, 3].imshow(ratio_diff, cmap='RdBu_r', origin='lower')
    axes[1, 3].set_title('比值差异 (RTM - 参考)')
    axes[1, 3].axis('off')
    plt.colorbar(im8, ax=axes[1, 3], shrink=0.8)
    
    # 第三行：MBMP检测结果
    im9 = axes[2, 0].imshow(delR, cmap='RdBu_r', origin='lower',
                           vmin=np.nanpercentile(delR, 5), vmax=np.nanpercentile(delR, 95))
    axes[2, 0].set_title('MBMP差分比值 (ΔR)')
    axes[2, 0].axis('off')
    plt.colorbar(im9, ax=axes[2, 0], shrink=0.8)
    
    im10 = axes[2, 1].imshow(omega, cmap='RdBu_r', origin='lower',
                            vmin=np.nanpercentile(omega, 5), vmax=np.nanpercentile(omega, 95))
    axes[2, 1].set_title('MBMP甲烷柱浓度 (mol/m²)')
    axes[2, 1].axis('off')
    plt.colorbar(im10, ax=axes[2, 1], shrink=0.8)
    
    # 甲烷检测结果（阈值0.1）
    threshold = 0.1
    methane_detection = omega > threshold
    im11 = axes[2, 2].imshow(methane_detection, cmap='RdYlGn', origin='lower')
    axes[2, 2].set_title(f'甲烷检测 (阈值>{threshold} mol/m²)')
    axes[2, 2].axis('off')
    plt.colorbar(im11, ax=axes[2, 2], shrink=0.8)
    
    # 羽流对比：原始 vs 检测
    axes[2, 3].imshow(original_plume, cmap='hot', origin='lower', alpha=0.7)
    axes[2, 3].contour(methane_detection, levels=[0.5], colors='blue', linewidths=2)
    axes[2, 3].set_title('羽流对比\n(红色:原始, 蓝线:检测)')
    axes[2, 3].axis('off')
    
    plt.suptitle('最终版：真实甲烷羽流MBMP检测完整分析', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"最终MBMP分析图已保存: {save_path}")
    plt.show()

def analyze_final_detection_accuracy(original_plume, omega, thresholds=[0.05, 0.1, 0.2, 0.5]):
    """分析最终检测精度"""
    print(f"\n🎯 最终检测精度分析:")
    print("=" * 50)
    
    # 创建真实羽流掩膜（不同阈值）
    plume_thresholds = [0.1, 0.5, 1.0]
    
    print("真实羽流阈值(ppm)\t检测阈值(mol/m²)\t准确率\t召回率\tF1分数")
    print("-" * 70)
    
    for plume_thresh in plume_thresholds:
        true_plume_mask = original_plume > plume_thresh
        true_plume_pixels = np.sum(true_plume_mask)
        
        if true_plume_pixels == 0:
            continue
            
        for detect_thresh in thresholds:
            detected_mask = omega > detect_thresh
            
            # 计算混淆矩阵
            tp = np.sum(true_plume_mask & detected_mask)  # 真正例
            fp = np.sum(~true_plume_mask & detected_mask)  # 假正例
            fn = np.sum(true_plume_mask & ~detected_mask)  # 假负例
            tn = np.sum(~true_plume_mask & ~detected_mask)  # 真负例
            
            # 计算指标
            precision = tp / (tp + fp) if (tp + fp) > 0 else 0
            recall = tp / (tp + fn) if (tp + fn) > 0 else 0
            f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
            
            print(f"{plume_thresh:12.1f}\t\t{detect_thresh:12.2f}\t{precision:.3f}\t{recall:.3f}\t{f1:.3f}")

def main():
    """主函数 - 最终版MBMP检测"""
    print("="*70)
    print("最终版：基于真实甲烷羽流RTM增强数据的MBMP检测")
    print("="*70)
    
    # 文件路径（使用绝对路径避免路径问题）
    RTM_ENHANCED_TIF = r"D:\jiawan3.11\7.26 - 副本 (2)\final_rtm_enhanced00000_sentinel2.tif"  # RTM增强影像
    REF_TIF = r"D:\jiawan3.11\7.26 - 副本 (2)\11\纬度38.4939_经度54.1976\S2_Combined_2021-06-23_15-19-01_NAORBIT_40SBH_N0300.tif"  # 参考影像
    EXCEL_FILE = r"D:\jiawan3.11\7.26 - 副本 (2)\11\纬度38.4939_经度54.1976\所有日期角度信息汇总.xlsx"  # 角度信息
    ORIGINAL_PLUME_NPY = r"D:\jiawan3.11\7.26 - 副本 (2)\11\methane_plume_Algeria_plume1_Q10000_ppm.npy"  # 原始羽流数据
    
    # 波段索引
    B11_INDEX = 3
    B12_INDEX = 4
    
    print("\n1. 加载RTM增强影像...")
    R11, R12 = extract_bands_from_5band_tif(RTM_ENHANCED_TIF, B11_INDEX, B12_INDEX)
    
    print("\n2. 加载参考影像...")
    R11old, R12old = extract_bands_from_5band_tif(REF_TIF, B11_INDEX, B12_INDEX)
    
    print("\n3. 加载原始甲烷羽流数据...")
    original_plume = load_original_plume(ORIGINAL_PLUME_NPY)
    
    if R11 is None or R12 is None or R11old is None or R12old is None or original_plume is None:
        print("❌ 数据加载失败，请检查文件路径")
        return
    
    print("\n4. 计算MBMP差分比值...")
    delR = mbmpk(R12, R11, R12old, R11old)
    
    print("\n5. 提取角度信息...")
    try:
        sza, vza = extract_sun_angles_from_excel(EXCEL_FILE, REF_TIF)
    except Exception as e:
        print(f"提取角度信息失败: {e}")
        print("使用默认角度值...")
        sza, vza = 20.72, 4.81
    
    print(f"\n6. 计算甲烷柱浓度...")
    print(f"使用角度 - 太阳天顶角: {sza:.2f}°, 观测天顶角: {vza:.2f}°")
    
    satellite = 'S2'
    omega = fullMBMP2Omega(delR, satellite, sza, vza)
    
    if omega is None:
        print("❌ MBMP转换失败，程序退出")
        return
    
    print(f"\n7. 最终MBMP检测结果统计:")
    print(f"甲烷柱浓度 (mol/m²):")
    print(f"  最大值: {np.nanmax(omega):.6f}")
    print(f"  最小值: {np.nanmin(omega):.6f}")
    print(f"  平均值: {np.nanmean(omega):.6f}")
    print(f"  标准差: {np.nanstd(omega):.6f}")
    
    # 甲烷检测统计（多个阈值）
    thresholds = [0.05, 0.1, 0.2, 0.5, 1.0]
    total_pixels = np.sum(~np.isnan(omega))
    
    print(f"\n甲烷检测统计:")
    print("阈值(mol/m²)\t检测像元数\t检测率(%)")
    print("-" * 40)
    
    for threshold in thresholds:
        positive_pixels = np.sum(omega > threshold)
        detection_rate = (positive_pixels / total_pixels) * 100
        print(f"{threshold:8.2f}\t\t{positive_pixels:8d}\t{detection_rate:7.2f}")
    
    print("\n8. 生成最终MBMP可视化结果...")
    create_final_mbmp_visualization(delR, omega, R11, R12, R11old, R12old, 
                                   original_plume, "final_mbmp_analysis.png")
    
    # 分析检测精度
    analyze_final_detection_accuracy(original_plume, omega)
    
    # 保存结果数据
    print("\n9. 保存最终MBMP结果数据...")
    output_meta = {
        'driver': 'GTiff',
        'dtype': 'float32',
        'nodata': np.nan,
        'width': omega.shape[1],
        'height': omega.shape[0],
        'count': 1,
        'crs': 'EPSG:3857',
        'compress': 'lzw'
    }
    
    with rasterio.open("final_mbmp_methane_column.tif", 'w', **output_meta) as dst:
        dst.write(omega.astype(np.float32), 1)
    print("最终MBMP甲烷柱浓度已保存: final_mbmp_methane_column.tif")
    
    with rasterio.open("final_mbmp_delta_r.tif", 'w', **output_meta) as dst:
        dst.write(delR.astype(np.float32), 1)
    print("最终MBMP差分比值已保存: final_mbmp_delta_r.tif")
    
    print("\n🎉 最终版RTM-MBMP检测完成！")
    print("现在可以看到基于真实羽流和RTM模型的准确检测效果了！")
    print("="*70)

if __name__ == "__main__":
    main()
