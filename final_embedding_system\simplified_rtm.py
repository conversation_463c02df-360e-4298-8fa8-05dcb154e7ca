#!/usr/bin/env python3
"""
简化辐射传输模型 (Simplified Radiative Transfer Model)
基于Beer-Lambert定律的甲烷吸收模拟

作者: AI Assistant
日期: 2025-07-27
版本: Final v1.0
"""

import numpy as np
from typing import Tuple, Dict, Optional

class SimplifiedRTM:
    """
    简化的辐射传输模型
    基于Beer-Lambert定律模拟甲烷在大气中的吸收效应
    """
    
    def __init__(self):
        """初始化RTM模型参数"""
        
        # 甲烷在不同波长的吸收系数 (最终优化版本)
        # 单位: cm²/molecule (针对真实羽流数据优化)
        self.methane_absorption_coeff = {
            'B11': 2.0e-17,  # B11波段甲烷吸收较强
            'B12': 5.0e-18   # B12波段甲烷吸收较弱 (保持差异性)
        }
        
        # 物理常数
        self.avogadro = 6.022e23  # 阿伏伽德罗常数
        self.air_density = 2.46e19  # 标准大气密度 (molecules/cm³)
        
        print("✅ 简化RTM模型初始化完成")
        print(f"   B11波段吸收系数: {self.methane_absorption_coeff['B11']:.2e} cm²/molecule")
        print(f"   B12波段吸收系数: {self.methane_absorption_coeff['B12']:.2e} cm²/molecule")
    
    def calculate_methane_absorption(self, methane_concentration: np.ndarray, 
                                   path_length: float = 1000.0,
                                   band: str = 'B11') -> np.ndarray:
        """
        计算甲烷吸收效应
        
        Args:
            methane_concentration: 甲烷浓度 (ppm)
            path_length: 光程长度 (m)
            band: 波段名称 ('B11' 或 'B12')
            
        Returns:
            透射率 (0-1)
        """
        # 将ppm转换为分子浓度 (molecules/cm³)
        # 1 ppm = 1e-6 * 空气密度
        concentration_molecules = methane_concentration * 1e-6 * self.air_density
        
        # 获取吸收系数
        abs_coeff = self.methane_absorption_coeff[band]
        
        # 计算光学厚度 (Beer-Lambert定律)
        # τ = σ * N * L
        optical_depth = abs_coeff * concentration_molecules * (path_length / 100)  # 转换为cm
        
        # 计算透射率
        # T = exp(-τ)
        transmittance = np.exp(-optical_depth)
        
        return transmittance
    
    def calculate_air_mass_factor(self, solar_zenith: float, view_zenith: float) -> float:
        """
        计算大气质量因子 (Air Mass Factor)
        
        Args:
            solar_zenith: 太阳天顶角 (度)
            view_zenith: 观测天顶角 (度)
            
        Returns:
            大气质量因子
        """
        import math
        
        # 转换为弧度
        sza_rad = math.radians(solar_zenith)
        vza_rad = math.radians(view_zenith)
        
        # 简化的AMF计算
        amf = 1.0 / math.cos(sza_rad) + 1.0 / math.cos(vza_rad)
        
        return amf
    
    def apply_methane_effect(self, sentinel2_data: np.ndarray,
                           methane_plume: np.ndarray,
                           solar_zenith: float = 30.0,
                           view_zenith: float = 0.0) -> np.ndarray:
        """
        将甲烷吸收效应应用到Sentinel-2数据
        
        Args:
            sentinel2_data: Sentinel-2数据 [bands, height, width]
            methane_plume: 甲烷羽流浓度 [height, width] (ppm)
            solar_zenith: 太阳天顶角 (度)
            view_zenith: 观测天顶角 (度)
            
        Returns:
            应用甲烷效应后的数据
        """
        # 复制原始数据
        enhanced_data = sentinel2_data.copy()
        
        # 计算大气质量因子
        amf = self.calculate_air_mass_factor(solar_zenith, view_zenith)
        effective_path_length = 1000.0 * amf  # 有效光程
        
        # 对B11和B12波段应用甲烷吸收
        band_indices = {'B11': 3, 'B12': 4}  # Sentinel-2波段索引
        
        for band_name, band_idx in band_indices.items():
            # 计算透射率
            transmittance = self.calculate_methane_absorption(
                methane_plume, effective_path_length, band_name
            )
            
            # 应用吸收效应
            enhanced_data[band_idx] = enhanced_data[band_idx] * transmittance
        
        return enhanced_data
    
    def get_model_info(self) -> Dict:
        """获取模型信息"""
        return {
            'model_name': 'Simplified RTM v1.0',
            'description': 'Beer-Lambert law based methane absorption model',
            'absorption_coefficients': self.methane_absorption_coeff,
            'physical_constants': {
                'avogadro': self.avogadro,
                'air_density': self.air_density
            }
        }

def test_rtm_model():
    """测试RTM模型"""
    print("🧪 测试简化RTM模型")
    print("=" * 40)
    
    # 创建模型
    rtm = SimplifiedRTM()
    
    # 创建测试数据
    test_concentration = np.array([[0, 1, 5], [10, 20, 50]])  # ppm
    
    # 测试B11和B12波段的吸收
    for band in ['B11', 'B12']:
        transmittance = rtm.calculate_methane_absorption(test_concentration, band=band)
        absorption = (1 - transmittance) * 100  # 转换为百分比
        
        print(f"\n{band}波段测试结果:")
        print(f"甲烷浓度 (ppm): {test_concentration}")
        print(f"透射率: {transmittance}")
        print(f"吸收率 (%): {absorption}")
    
    # 测试AMF计算
    amf = rtm.calculate_air_mass_factor(30.0, 5.0)
    print(f"\n大气质量因子 (SZA=30°, VZA=5°): {amf:.3f}")
    
    print("\n✅ RTM模型测试完成")

if __name__ == "__main__":
    test_rtm_model()
