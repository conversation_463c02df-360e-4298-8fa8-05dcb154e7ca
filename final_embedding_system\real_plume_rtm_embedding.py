#!/usr/bin/env python3
"""
最终版本：真实甲烷羽流RTM嵌入系统
使用简化RTM模型将真实甲烷羽流嵌入到Sentinel-2影像中

作者: AI Assistant  
日期: 2025-07-27
版本: Final v1.0
"""

import numpy as np
import rasterio
import matplotlib.pyplot as plt
from simplified_rtm import SimplifiedRTM
import os
from typing import Tuple, Dict, Optional

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 100

class FinalRealPlumeRTMEmbedding:
    """最终版本：真实甲烷羽流RTM嵌入系统"""
    
    def __init__(self):
        """初始化嵌入系统"""
        self.rtm = SimplifiedRTM()
        print("✅ 最终版RTM嵌入系统已初始化")
    
    def load_real_methane_plume(self, npy_file: str) -> np.ndarray:
        """加载真实甲烷羽流数据"""
        try:
            plume_data = np.load(npy_file)
            print(f"✅ 真实甲烷羽流数据加载成功:")
            print(f"   文件: {os.path.basename(npy_file)}")
            print(f"   形状: {plume_data.shape}")
            print(f"   数据类型: {plume_data.dtype}")
            print(f"   浓度范围: {np.nanmin(plume_data):.3f} - {np.nanmax(plume_data):.3f} ppm")
            print(f"   平均浓度: {np.nanmean(plume_data):.3f} ppm")
            print(f"   非零像元数: {np.sum(plume_data > 0)}")
            
            return plume_data
            
        except Exception as e:
            print(f"❌ 甲烷羽流数据加载失败: {e}")
            return None
    
    def load_sentinel2_data(self, file_path: str) -> Tuple[np.ndarray, dict]:
        """加载Sentinel-2数据（修正方向问题）"""
        try:
            with rasterio.open(file_path) as src:
                data = src.read().astype(np.float32)
                meta = src.meta.copy()
                
                # 修复方向问题：rasterio读取的数据需要上下翻转以匹配显示坐标系
                # 因为rasterio使用地理坐标系（北向上），而matplotlib默认使用图像坐标系（南向上）
                data = np.flip(data, axis=1)  # 沿着height维度翻转
                print("✅ 已修正数据方向以匹配显示坐标系")
                
                # 如果是数字计数值，转换为反射率
                if np.max(data) > 10:
                    data = data * 0.0001
                    print("数据已转换为反射率")
                
                print(f"✅ Sentinel-2数据加载成功: {data.shape}")
                return data, meta
                
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return None, None
    
    def resize_plume_to_match(self, plume_data: np.ndarray, 
                             target_shape: Tuple[int, int]) -> np.ndarray:
        """调整羽流数据尺寸以匹配Sentinel-2数据"""
        from scipy import ndimage
        
        current_shape = plume_data.shape
        target_height, target_width = target_shape
        
        print(f"🔄 调整羽流尺寸:")
        print(f"   原始尺寸: {current_shape}")
        print(f"   目标尺寸: {target_shape}")
        
        if current_shape == target_shape:
            print("   尺寸已匹配，无需调整")
            return plume_data
        
        # 使用双线性插值调整尺寸
        zoom_factors = (target_height / current_shape[0], 
                       target_width / current_shape[1])
        
        resized_plume = ndimage.zoom(plume_data, zoom_factors, order=1)
        
        print(f"   调整后尺寸: {resized_plume.shape}")
        print(f"   调整后浓度范围: {np.nanmin(resized_plume):.3f} - {np.nanmax(resized_plume):.3f} ppm")
        
        return resized_plume
    
    def embed_real_plume_with_rtm(self, sentinel2_data: np.ndarray,
                                 real_plume: np.ndarray,
                                 solar_zenith: float = 30.0,
                                 view_zenith: float = 0.0) -> np.ndarray:
        """
        使用RTM模型嵌入真实甲烷羽流
        
        Args:
            sentinel2_data: Sentinel-2数据 [bands, height, width]
            real_plume: 真实甲烷羽流浓度 [height, width] (ppm)
            solar_zenith: 太阳天顶角 (度)
            view_zenith: 观测天顶角 (度)
            
        Returns:
            嵌入羽流后的数据
        """
        print("🔬 开始RTM真实甲烷羽流嵌入...")
        
        # 确保羽流数据尺寸匹配
        target_shape = sentinel2_data.shape[1:]  # (height, width)
        if real_plume.shape != target_shape:
            real_plume = self.resize_plume_to_match(real_plume, target_shape)
        
        # 应用RTM模型
        enhanced_data = self.rtm.apply_methane_effect(
            sentinel2_data, real_plume, solar_zenith, view_zenith
        )
        
        # 计算嵌入效果统计
        original_b11 = sentinel2_data[3]  # B11波段
        original_b12 = sentinel2_data[4]  # B12波段
        enhanced_b11 = enhanced_data[3]
        enhanced_b12 = enhanced_data[4]
        
        # 计算比值变化
        original_ratio = original_b11 / (original_b12 + 1e-8)
        enhanced_ratio = enhanced_b11 / (enhanced_b12 + 1e-8)
        ratio_change = enhanced_ratio - original_ratio
        
        print(f"✅ RTM真实羽流嵌入完成:")
        print(f"   B11波段平均变化: {np.mean(enhanced_b11 - original_b11):.6f}")
        print(f"   B12波段平均变化: {np.mean(enhanced_b12 - original_b12):.6f}")
        print(f"   B11/B12比值平均变化: {np.mean(ratio_change):.6f}")
        print(f"   最大比值变化: {np.max(ratio_change):.6f}")
        print(f"   羽流区域比值变化: {np.mean(ratio_change[real_plume > 1]):.6f}")
        
        return enhanced_data
    
    def create_final_visualization(self, original_data: np.ndarray,
                                  enhanced_data: np.ndarray,
                                  real_plume: np.ndarray,
                                  save_path: str = "final_rtm_embedding_analysis.png"):
        """创建最终版RTM嵌入分析可视化"""
        fig, axes = plt.subplots(3, 4, figsize=(20, 15))
        
        band_names = ['B3', 'B4', 'B8', 'B11', 'B12']
        
        # 第一行：原始数据
        for i in range(4):
            if i < len(band_names):
                im = axes[0, i].imshow(original_data[i], cmap='viridis', origin='lower')
                axes[0, i].set_title(f'原始 {band_names[i]}')
                axes[0, i].axis('off')
                plt.colorbar(im, ax=axes[0, i], shrink=0.8)
        
        # 第二行：RTM增强后数据
        for i in range(4):
            if i < len(band_names):
                im = axes[1, i].imshow(enhanced_data[i], cmap='viridis', origin='lower')
                axes[1, i].set_title(f'RTM增强 {band_names[i]}')
                axes[1, i].axis('off')
                plt.colorbar(im, ax=axes[1, i], shrink=0.8)
        
        # 第三行：羽流分析
        # 真实甲烷羽流
        im1 = axes[2, 0].imshow(real_plume, cmap='hot', origin='lower')
        axes[2, 0].set_title('真实甲烷羽流 (ppm)')
        axes[2, 0].axis('off')
        plt.colorbar(im1, ax=axes[2, 0], shrink=0.8)
        
        # B11差异
        b11_diff = enhanced_data[3] - original_data[3]
        im2 = axes[2, 1].imshow(b11_diff, cmap='RdBu_r', origin='lower')
        axes[2, 1].set_title('B11波段变化')
        axes[2, 1].axis('off')
        plt.colorbar(im2, ax=axes[2, 1], shrink=0.8)
        
        # B12差异
        b12_diff = enhanced_data[4] - original_data[4]
        im3 = axes[2, 2].imshow(b12_diff, cmap='RdBu_r', origin='lower')
        axes[2, 2].set_title('B12波段变化')
        axes[2, 2].axis('off')
        plt.colorbar(im3, ax=axes[2, 2], shrink=0.8)
        
        # B11/B12比值变化
        original_ratio = original_data[3] / (original_data[4] + 1e-8)
        enhanced_ratio = enhanced_data[3] / (enhanced_data[4] + 1e-8)
        ratio_change = enhanced_ratio - original_ratio
        
        im4 = axes[2, 3].imshow(ratio_change, cmap='RdBu_r', origin='lower')
        axes[2, 3].set_title('B11/B12比值变化')
        axes[2, 3].axis('off')
        plt.colorbar(im4, ax=axes[2, 3], shrink=0.8)
        
        plt.suptitle('最终版：真实甲烷羽流RTM嵌入效果分析', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"最终RTM分析图已保存: {save_path}")
        plt.show()
    
    def analyze_final_rtm_effects(self, original_data: np.ndarray,
                                 enhanced_data: np.ndarray,
                                 real_plume: np.ndarray):
        """分析最终RTM效应"""
        print("\n📊 最终真实甲烷羽流RTM效应分析:")
        print("=" * 60)
        
        # 找到甲烷浓度最高的区域
        max_methane_idx = np.unravel_index(np.argmax(real_plume), real_plume.shape)
        max_concentration = real_plume[max_methane_idx]
        
        print(f"最高甲烷浓度位置: {max_methane_idx}")
        print(f"最高甲烷浓度: {max_concentration:.2f} ppm")
        
        # 分析不同浓度区域的效应
        concentration_thresholds = [1, 5, 10, 20]
        
        print(f"\n不同浓度区域的RTM效应:")
        print("浓度阈值(ppm)\t像元数\tB11变化\t\tB12变化\t\t比值变化")
        print("-" * 70)
        
        for threshold in concentration_thresholds:
            mask = real_plume > threshold
            pixel_count = np.sum(mask)
            
            if pixel_count > 0:
                b11_change = np.mean((enhanced_data[3] - original_data[3])[mask])
                b12_change = np.mean((enhanced_data[4] - original_data[4])[mask])
                
                original_ratio = original_data[3] / (original_data[4] + 1e-8)
                enhanced_ratio = enhanced_data[3] / (enhanced_data[4] + 1e-8)
                ratio_change = np.mean((enhanced_ratio - original_ratio)[mask])
                
                print(f"{threshold:8d}\t\t{pixel_count:6d}\t{b11_change:+.6f}\t{b12_change:+.6f}\t{ratio_change:+.6f}")
        
        # 羽流形状分析
        print(f"\n羽流形状特征:")
        plume_pixels = np.sum(real_plume > 0.1)
        total_pixels = real_plume.size
        plume_coverage = (plume_pixels / total_pixels) * 100
        
        print(f"羽流覆盖像元数: {plume_pixels}")
        print(f"羽流覆盖率: {plume_coverage:.2f}%")
        print(f"羽流平均浓度: {np.mean(real_plume[real_plume > 0.1]):.2f} ppm")

def main():
    """主函数 - 最终版真实甲烷羽流RTM嵌入"""
    print("🚀 启动最终版真实甲烷羽流RTM嵌入系统")
    print("=" * 70)

    # 初始化系统
    embedder = FinalRealPlumeRTMEmbedding()

    # 文件路径（请根据实际情况修改）
    plume_file = r"D:\jiawan3.11\7.27\11\methane_plume_Algeria_plume0_Q7000_ppm.npy"
    input_file = r"D:\jiawan3.11\7.27\11\纬度38.4939_经度54.1976\S2_Combined_2021-06-13_15-19-01_NAORBIT_40SBH_N0300.tif"
    
    # 加载真实甲烷羽流数据
    print(f"\n📂 加载真实甲烷羽流数据: {plume_file}")
    real_plume = embedder.load_real_methane_plume(plume_file)
    
    if real_plume is None:
        print("❌ 甲烷羽流数据加载失败，程序退出")
        return
    
    # 加载Sentinel-2数据
    print(f"\n📂 加载Sentinel-2数据: {input_file}")
    sentinel2_data, meta = embedder.load_sentinel2_data(input_file)
    
    if sentinel2_data is None:
        print("❌ Sentinel-2数据加载失败，程序退出")
        return
    
    # 使用RTM嵌入真实羽流
    print(f"\n🔬 使用RTM模型嵌入真实甲烷羽流...")
    enhanced_data = embedder.embed_real_plume_with_rtm(
        sentinel2_data=sentinel2_data,
        real_plume=real_plume,
        solar_zenith=20.72,  # 使用实际角度
        view_zenith=4.81
    )
    
    # 保存结果
    output_file = "final_rtm_enhanced00000_sentinel2.tif"
    print(f"\n💾 保存最终RTM增强数据...")
    
    try:
        output_meta = meta.copy()
        output_meta.update({
            'dtype': 'float32',
            'count': enhanced_data.shape[0],
            'compress': 'lzw'
        })
        
        with rasterio.open(output_file, 'w', **output_meta) as dst:
            dst.write(enhanced_data)
        
        print(f"✅ 最终RTM增强数据已保存: {output_file}")
        
    except Exception as e:
        print(f"❌ 保存失败: {e}")
    
    # 创建可视化分析
    print(f"\n📊 创建最终RTM效果可视化...")
    embedder.create_final_visualization(
        original_data=sentinel2_data,
        enhanced_data=enhanced_data,
        real_plume=real_plume,
        save_path="final_rtm_embedding_analysis.png"
    )
    
    # 分析RTM效应
    embedder.analyze_final_rtm_effects(
        original_data=sentinel2_data,
        enhanced_data=enhanced_data,
        real_plume=real_plume
    )
    
    print(f"\n🎉 最终版真实甲烷羽流RTM嵌入完成！")
    print(f"输出文件: {output_file}")
    print("现在可以用这个数据进行MBMP检测，获得准确的羽流检测结果！")
    print("=" * 70)

if __name__ == "__main__":
    main()
