#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交互式Sentinel-2波段探索工具
提供更多的可视化选项和分析功能
"""

import numpy as np
import matplotlib.pyplot as plt
import rasterio
import os
from matplotlib.widgets import Slider, Button
from matplotlib.colors import LinearSegmentedColormap
import seaborn as sns

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")

class Sentinel2BandExplorer:
    def __init__(self, tif_file):
        self.tif_file = tif_file
        self.data = None
        self.meta = None
        self.band_names = ['B3 (Green)', 'B4 (Red)', 'B8 (NIR)', 'B11 (SWIR1)', 'B12 (SWIR2)']
        self.load_data()
    
    def load_data(self):
        """加载数据"""
        try:
            with rasterio.open(self.tif_file) as src:
                self.data = src.read().astype(np.float32)
                self.meta = src.meta.copy()
                print(f"✅ 数据加载成功: {self.data.shape}")
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
    
    def create_rgb_composite(self, save_path="rgb_composite.png"):
        """创建RGB合成图像"""
        if self.data is None:
            return
        
        # 使用B4(Red), B3(Green), B8(NIR)创建假彩色合成
        red = self.data[1]    # B4
        green = self.data[0]  # B3  
        nir = self.data[2]    # B8
        
        # 归一化到0-1范围
        def normalize_band(band):
            return (band - np.nanmin(band)) / (np.nanmax(band) - np.nanmin(band))
        
        red_norm = normalize_band(red)
        green_norm = normalize_band(green)
        nir_norm = normalize_band(nir)
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 15))
        
        # 真彩色合成 (B4, B3, B8 -> R, G, B)
        rgb_true = np.stack([red_norm, green_norm, nir_norm], axis=-1)
        axes[0, 0].imshow(rgb_true, origin='lower')
        axes[0, 0].set_title('假彩色合成 (B4-B3-B8)')
        axes[0, 0].axis('off')
        
        # 单独显示各波段
        im1 = axes[0, 1].imshow(red, cmap='Reds', origin='lower')
        axes[0, 1].set_title('B4 (Red) 波段')
        axes[0, 1].axis('off')
        plt.colorbar(im1, ax=axes[0, 1], shrink=0.8)
        
        im2 = axes[1, 0].imshow(green, cmap='Greens', origin='lower')
        axes[1, 0].set_title('B3 (Green) 波段')
        axes[1, 0].axis('off')
        plt.colorbar(im2, ax=axes[1, 0], shrink=0.8)
        
        im3 = axes[1, 1].imshow(nir, cmap='RdYlBu_r', origin='lower')
        axes[1, 1].set_title('B8 (NIR) 波段')
        axes[1, 1].axis('off')
        plt.colorbar(im3, ax=axes[1, 1], shrink=0.8)
        
        plt.suptitle('Sentinel-2 RGB合成与单波段显示', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"✅ RGB合成图已保存: {save_path}")
        plt.show()
    
    def create_swir_analysis(self, save_path="swir_analysis.png"):
        """创建SWIR波段分析（重点关注甲烷检测相关波段）"""
        if self.data is None or self.data.shape[0] < 5:
            return
        
        b11 = self.data[3]  # B11 (SWIR1)
        b12 = self.data[4]  # B12 (SWIR2)
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('SWIR波段分析 - 甲烷检测关键波段', fontsize=16, fontweight='bold')
        
        # B11波段
        im1 = axes[0, 0].imshow(b11, cmap='viridis', origin='lower')
        axes[0, 0].set_title('B11 (SWIR1) - 1610nm\n甲烷吸收波段')
        axes[0, 0].axis('off')
        plt.colorbar(im1, ax=axes[0, 0], shrink=0.8)
        
        # B12波段
        im2 = axes[0, 1].imshow(b12, cmap='viridis', origin='lower')
        axes[0, 1].set_title('B12 (SWIR2) - 2190nm\n参考波段')
        axes[0, 1].axis('off')
        plt.colorbar(im2, ax=axes[0, 1], shrink=0.8)
        
        # B11/B12比值
        ratio = b11 / (b12 + 1e-8)
        im3 = axes[0, 2].imshow(ratio, cmap='RdYlBu_r', origin='lower')
        axes[0, 2].set_title('B11/B12 比值\n甲烷指示指标')
        axes[0, 2].axis('off')
        plt.colorbar(im3, ax=axes[0, 2], shrink=0.8)
        
        # B11-B12差值
        diff = b11 - b12
        im4 = axes[1, 0].imshow(diff, cmap='RdBu_r', origin='lower')
        axes[1, 0].set_title('B11 - B12 差值')
        axes[1, 0].axis('off')
        plt.colorbar(im4, ax=axes[1, 0], shrink=0.8)
        
        # 统计分析
        axes[1, 1].hist(ratio.flatten(), bins=50, alpha=0.7, color='skyblue', edgecolor='black')
        axes[1, 1].set_title('B11/B12比值分布')
        axes[1, 1].set_xlabel('比值')
        axes[1, 1].set_ylabel('像素数量')
        axes[1, 1].grid(True, alpha=0.3)
        
        # 散点图分析
        axes[1, 2].scatter(b12.flatten(), b11.flatten(), alpha=0.5, s=1)
        axes[1, 2].set_xlabel('B12 反射率')
        axes[1, 2].set_ylabel('B11 反射率')
        axes[1, 2].set_title('B11 vs B12 散点图')
        axes[1, 2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"✅ SWIR分析图已保存: {save_path}")
        plt.show()
    
    def create_spectral_profiles(self, save_path="spectral_profiles.png"):
        """创建光谱剖面分析"""
        if self.data is None:
            return
        
        # 选择几个代表性像素点
        h, w = self.data.shape[1], self.data.shape[2]
        sample_points = [
            (h//4, w//4, "左上区域"),
            (h//2, w//2, "中心区域"),
            (3*h//4, 3*w//4, "右下区域"),
            (h//4, 3*w//4, "右上区域"),
            (3*h//4, w//4, "左下区域")
        ]
        
        # 波段中心波长 (nm)
        wavelengths = [560, 665, 842, 1610, 2190]  # B3, B4, B8, B11, B12
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 光谱剖面图
        for i, (y, x, label) in enumerate(sample_points):
            spectrum = [self.data[band, y, x] for band in range(self.data.shape[0])]
            ax1.plot(wavelengths, spectrum, 'o-', label=f'{label} ({x},{y})', linewidth=2, markersize=6)
        
        ax1.set_xlabel('波长 (nm)')
        ax1.set_ylabel('反射率')
        ax1.set_title('不同区域的光谱剖面')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 平均光谱
        mean_spectrum = [np.nanmean(self.data[band]) for band in range(self.data.shape[0])]
        std_spectrum = [np.nanstd(self.data[band]) for band in range(self.data.shape[0])]
        
        ax2.errorbar(wavelengths, mean_spectrum, yerr=std_spectrum, 
                    fmt='o-', capsize=5, capthick=2, linewidth=2, markersize=8)
        ax2.set_xlabel('波长 (nm)')
        ax2.set_ylabel('反射率')
        ax2.set_title('整幅影像平均光谱 (±标准差)')
        ax2.grid(True, alpha=0.3)
        
        # 添加波段标注
        for i, (wl, name) in enumerate(zip(wavelengths, self.band_names)):
            ax2.annotate(name, (wl, mean_spectrum[i]), 
                        xytext=(10, 10), textcoords='offset points',
                        bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7))
        
        plt.suptitle('Sentinel-2 光谱特征分析', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"✅ 光谱剖面图已保存: {save_path}")
        plt.show()
    
    def create_enhanced_visualization(self, save_path="enhanced_visualization.png"):
        """创建增强版综合可视化"""
        if self.data is None:
            return
        
        fig = plt.figure(figsize=(20, 16))
        
        # 创建复杂的子图布局
        gs = fig.add_gridspec(4, 5, hspace=0.3, wspace=0.3)
        
        # 第一行：各波段显示
        for i in range(min(5, self.data.shape[0])):
            ax = fig.add_subplot(gs[0, i])
            band_data = self.data[i]
            vmin, vmax = np.nanpercentile(band_data, [2, 98])
            im = ax.imshow(band_data, cmap='viridis', origin='lower', vmin=vmin, vmax=vmax)
            ax.set_title(f'{self.band_names[i]}', fontsize=10)
            ax.axis('off')
            plt.colorbar(im, ax=ax, shrink=0.6)
        
        # 第二行：比值和差值分析
        if self.data.shape[0] >= 5:
            # NDVI (B8-B4)/(B8+B4)
            ax1 = fig.add_subplot(gs[1, 0])
            ndvi = (self.data[2] - self.data[1]) / (self.data[2] + self.data[1] + 1e-8)
            im1 = ax1.imshow(ndvi, cmap='RdYlGn', origin='lower')
            ax1.set_title('NDVI', fontsize=10)
            ax1.axis('off')
            plt.colorbar(im1, ax=ax1, shrink=0.6)
            
            # B11/B12比值
            ax2 = fig.add_subplot(gs[1, 1])
            ratio = self.data[3] / (self.data[4] + 1e-8)
            im2 = ax2.imshow(ratio, cmap='RdYlBu_r', origin='lower')
            ax2.set_title('B11/B12', fontsize=10)
            ax2.axis('off')
            plt.colorbar(im2, ax=ax2, shrink=0.6)
            
            # B11-B12差值
            ax3 = fig.add_subplot(gs[1, 2])
            diff = self.data[3] - self.data[4]
            im3 = ax3.imshow(diff, cmap='RdBu_r', origin='lower')
            ax3.set_title('B11-B12', fontsize=10)
            ax3.axis('off')
            plt.colorbar(im3, ax=ax3, shrink=0.6)
        
        # 第三行：统计分析
        ax4 = fig.add_subplot(gs[2, :3])
        wavelengths = [560, 665, 842, 1610, 2190]
        mean_spectrum = [np.nanmean(self.data[i]) for i in range(self.data.shape[0])]
        ax4.plot(wavelengths, mean_spectrum, 'o-', linewidth=3, markersize=8)
        ax4.set_xlabel('波长 (nm)')
        ax4.set_ylabel('平均反射率')
        ax4.set_title('平均光谱曲线')
        ax4.grid(True, alpha=0.3)
        
        # 第四行：直方图
        for i in range(min(3, self.data.shape[0])):
            ax = fig.add_subplot(gs[3, i])
            band_data = self.data[i]
            ax.hist(band_data.flatten(), bins=50, alpha=0.7, color=plt.cm.viridis(i/5))
            ax.set_title(f'{self.band_names[i]} 分布')
            ax.set_xlabel('反射率')
            ax.set_ylabel('频次')
        
        plt.suptitle('Sentinel-2 综合分析面板', fontsize=18, fontweight='bold')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"✅ 增强版可视化图已保存: {save_path}")
        plt.show()

def main():
    """主函数"""
    print("="*70)
    print("交互式 Sentinel-2 波段探索工具")
    print("="*70)
    
    tif_file = "final_rtm_enhanced00000_sentinel2.tif"
    
    if not os.path.exists(tif_file):
        print(f"❌ 文件不存在: {tif_file}")
        return
    
    # 创建探索器实例
    explorer = Sentinel2BandExplorer(tif_file)
    
    if explorer.data is None:
        return
    
    print(f"\n开始创建高级可视化分析...")
    
    # 创建各种分析图
    explorer.create_rgb_composite()
    explorer.create_swir_analysis()
    explorer.create_spectral_profiles()
    explorer.create_enhanced_visualization()
    
    print(f"\n🎉 所有高级可视化分析完成！")

if __name__ == "__main__":
    main()
