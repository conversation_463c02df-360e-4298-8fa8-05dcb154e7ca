# 最终版甲烷羽流RTM嵌入与MBMP检测系统

## 系统概述

这是一个完整的甲烷羽流检测系统，包含：
1. **简化辐射传输模型 (RTM)** - 基于Beer-Lambert定律模拟甲烷吸收
2. **真实羽流RTM嵌入** - 将真实甲烷羽流嵌入到Sentinel-2影像中
3. **MBMP检测算法** - 基于RTM增强数据进行甲烷检测

## 文件结构

```
final_embedding_system/
├── simplified_rtm.py              # 简化RTM模型核心代码
├── real_plume_rtm_embedding.py    # 真实羽流RTM嵌入主程序
├── pure_mbmp_detection.py         # 纯净版MBMP检测程序（推荐）
├── final_mbmp_detection.py        # 完整版MBMP检测程序（包含验证）
├── validation_script.py           # 验证脚本（仅用于开发阶段）
├── README.md                      # 本说明文件
└── requirements.txt               # 依赖包列表
```

## 系统特点

### 1. 简化RTM模型 (simplified_rtm.py)
- **基于物理原理**：使用Beer-Lambert定律模拟甲烷吸收
- **优化参数**：针对真实羽流数据优化的吸收系数
  - B11波段: 2.0e-17 cm²/molecule
  - B12波段: 5.0e-18 cm²/molecule
- **角度修正**：考虑太阳天顶角和观测天顶角的影响
- **无需6S**：避免复杂的6S软件安装

### 2. 真实羽流RTM嵌入 (real_plume_rtm_embedding.py)
- **方向修正**：解决了rasterio读取数据的方向问题
- **真实数据**：使用Algeria真实甲烷羽流数据
- **完整可视化**：生成详细的RTM效果分析图
- **效果显著**：在高浓度区域产生明显的B11/B12比值变化

### 3. 纯净版MBMP检测 (pure_mbmp_detection.py) ⭐推荐
- **真正的盲检测**：不依赖任何原始羽流信息
- **基于影像数据**：纯粹从Sentinel-2影像中检测甲烷
- **多阈值分析**：提供不同阈值下的检测统计
- **真实检测算法**：符合实际应用场景的检测流程

### 4. 完整版MBMP检测 (final_mbmp_detection.py)
- **包含验证功能**：加载原始羽流数据进行对比（仅用于开发）
- **精度分析**：提供详细的检测精度评估
- **完整可视化**：生成从原始羽流到检测结果的完整分析图

### 5. 验证脚本 (validation_script.py)
- **开发工具**：仅用于算法开发阶段的性能评估
- **多阈值验证**：分析不同参数组合的检测效果
- **性能指标**：计算准确率、召回率、F1分数等

## 使用方法

### 1. 环境准备

```bash
# 安装依赖包
pip install numpy matplotlib rasterio scipy pandas openpyxl
```

### 2. 数据准备

确保以下数据文件存在：
- `../11/methane_plume_Algeria_plume0_Q7000_ppm.npy` - 真实甲烷羽流数据
- `../11/纬度38.4939_经度54.1976/S2_Combined_2021-06-23_15-19-01_NAORBIT_40SBH_N0300.tif` - Sentinel-2参考影像
- `../11/纬度38.4939_经度54.1976/所有日期角度信息汇总.xlsx` - 角度信息文件
- `../11/mbmp/S2_full_mdata_poly_10_delr_to_omega.pkl` - MBMP转换系数

### 3. 运行步骤

#### 步骤1：RTM嵌入
```bash
python real_plume_rtm_embedding.py
```

**输出文件：**
- `final_rtm_enhanced_sentinel2.tif` - RTM增强的Sentinel-2数据
- `final_rtm_embedding_analysis.png` - RTM嵌入效果分析图

#### 步骤2：MBMP检测（推荐使用纯净版）
```bash
# 纯净版检测（推荐）- 真正的盲检测
python pure_mbmp_detection.py
```

**输出文件：**
- `pure_mbmp_methane_column.tif` - 甲烷柱浓度检测结果
- `pure_mbmp_delta_r.tif` - MBMP差分比值
- `pure_mbmp_detection.png` - 纯净版检测结果图

#### 可选：验证分析（仅用于开发）
```bash
# 验证检测效果（仅用于算法开发阶段）
python validation_script.py
```

**输出文件：**
- `validation_results.png` - 检测效果验证分析图

## 技术细节

### RTM模型原理

基于Beer-Lambert定律：
```
T = exp(-σ × N × L)
```

其中：
- T: 透射率
- σ: 甲烷吸收截面 (cm²/molecule)
- N: 甲烷分子浓度 (molecules/cm³)
- L: 光程长度 (cm)

### 方向修正

解决了以下问题：
1. **rasterio坐标系**：地理坐标系（北向上）
2. **matplotlib坐标系**：图像坐标系（可选南向上或北向上）
3. **数组索引**：numpy数组的行列索引

通过在数据加载时进行适当的翻转操作，确保整个流程中数据方向的一致性。

### MBMP算法

1. **归一化**：对B11和B12波段进行中值归一化
2. **比值计算**：计算B12/B11比值
3. **差分**：计算含甲烷影像与参考影像的比值差
4. **转换**：使用多项式系数将差分比值转换为甲烷柱浓度

## 结果解读

### RTM嵌入效果
- **B11波段变化**：甲烷吸收导致反射率降低
- **B12波段变化**：相对较小的变化
- **B11/B12比值变化**：明显的负变化，表明甲烷吸收效应

### MBMP检测结果
- **甲烷柱浓度**：单位为 mol/m²
- **检测阈值**：通常使用 0.1 mol/m² 作为检测阈值
- **精度指标**：准确率、召回率、F1分数

## 系统优势

1. **物理基础**：基于真实的物理原理，不是纯经验模型
2. **真实数据**：使用真实的Algeria甲烷羽流数据
3. **方向正确**：解决了图像方向问题，确保结果可靠
4. **完整流程**：从RTM嵌入到MBMP检测的完整链条
5. **详细分析**：提供丰富的可视化和统计分析

## 注意事项

1. **文件路径**：请根据实际情况修改代码中的文件路径
2. **数据格式**：确保输入数据格式正确
3. **内存使用**：处理大尺寸影像时注意内存使用
4. **参数调整**：可根据具体应用调整RTM参数和检测阈值

## 版本信息

- **版本**：Final v1.0
- **日期**：2025-07-27
- **作者**：AI Assistant
- **基于**：Martin Monier 2023论文方法

## 联系方式

如有问题或建议，请联系开发者。

---

**祝您使用愉快！** 🚀
